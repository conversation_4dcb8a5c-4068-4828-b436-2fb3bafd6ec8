/**
 * 剪贴板工具函数
 * 提供复制文本到剪贴板的功能，支持现代API和降级方案
 */

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {Object} options - 选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 失败回调
 * @param {boolean} options.showMessage - 是否显示消息提示（需要传入Vue实例）
 * @param {Object} options.vm - Vue实例（用于显示消息）
 * @returns {Promise<boolean>} 是否成功
 */
export async function copyToClipboard(text, options = {}) {
  const {
    onSuccess,
    onError,
    showMessage = false,
    vm = null
  } = options

  if (!text || typeof text !== 'string') {
    const error = new Error('复制内容不能为空')
    if (onError) onError(error)
    if (showMessage && vm && vm.$message) {
      vm.$message.error('复制内容不能为空')
    }
    return false
  }

  try {
    let success = false

    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      success = true
    } else {
      // 降级方案：使用 document.execCommand
      success = fallbackCopyTextToClipboard(text)
    }

    if (success) {
      if (onSuccess) onSuccess()
      if (showMessage && vm && vm.$message) {
        vm.$message.success('复制成功')
      }
      return true
    } else {
      throw new Error('复制操作失败')
    }
  } catch (error) {
    console.error('复制失败:', error)
    if (onError) onError(error)
    if (showMessage && vm && vm.$message) {
      vm.$message.error('复制失败')
    }
    return false
  }
}

/**
 * 降级复制方案
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否成功
 */
function fallbackCopyTextToClipboard(text) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  
  // 避免滚动到底部
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.position = 'fixed'
  textArea.style.opacity = '0'
  textArea.style.pointerEvents = 'none'
  
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  
  try {
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    return successful
  } catch (err) {
    console.error('降级复制方案失败:', err)
    document.body.removeChild(textArea)
    return false
  }
}

/**
 * 提取纯文本内容（去除HTML标签和Markdown格式）
 * @param {string} content - 原始内容
 * @returns {string} 纯文本内容
 */
export function extractPlainText(content) {
  if (!content || typeof content !== 'string') {
    return ''
  }

  // 移除HTML标签
  let text = content.replace(/<[^>]*>/g, '')
  
  // 移除常见的Markdown格式
  text = text
    .replace(/```[\s\S]*?```/g, '') // 代码块
    .replace(/`([^`]+)`/g, '$1') // 行内代码
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 粗体
    .replace(/\*([^*]+)\*/g, '$1') // 斜体
    .replace(/~~([^~]+)~~/g, '$1') // 删除线
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 链接
    .replace(/^#+\s+/gm, '') // 标题
    .replace(/^[-*+]\s+/gm, '') // 列表
    .replace(/^\d+\.\s+/gm, '') // 有序列表
    .replace(/^\>\s+/gm, '') // 引用
  
  // 清理多余的空白字符
  text = text
    .replace(/\n\s*\n/g, '\n') // 多个换行合并为一个
    .replace(/[ \t]+/g, ' ') // 多个空格合并为一个
    .trim()
  
  return text
}

/**
 * 创建一个带有默认配置的复制函数
 * @param {Object} vm - Vue实例
 * @returns {Function} 配置好的复制函数
 */
export function createCopyFunction(vm) {
  return (text) => {
    return copyToClipboard(text, {
      showMessage: true,
      vm
    })
  }
}

/**
 * Vue插件形式的剪贴板功能
 * 使用方式：Vue.use(ClipboardPlugin)
 * 然后在组件中：this.$copyText('要复制的文本')
 */
export const ClipboardPlugin = {
  install(Vue) {
    Vue.prototype.$copyText = function(text) {
      return copyToClipboard(text, {
        showMessage: true,
        vm: this
      })
    }
  }
}

export default {
  copyToClipboard,
  extractPlainText,
  createCopyFunction,
  ClipboardPlugin
}
